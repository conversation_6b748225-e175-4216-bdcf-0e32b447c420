import asyncio
import edge_tts
import re
import os
from mutagen.mp3 import MP3

# Settings
SOURCE_FILE = 'file.txt'
VOICE = "ru-RU-DmitryNeural" #ru-RU-SvetlanaNeural ru-RU-DmitryNeural fi-FI-HarriNeural fi-FI-NooraNeural en-GB-RyanNeural
MAX_DURATION_MINUTES = 10  # Maximum duration per MP3 file in minutes
MAX_DURATION_SECONDS = MAX_DURATION_MINUTES * 60  # Convert to seconds
INITIAL_CHUNK_WORDS = 1300  # Starting estimate for words per chunk
PAUSE_BETWEEN_REQUESTS = 0

# Read text from the file
def read_text_from_file(text_file) -> str:
    try:
        with open(text_file, 'r', encoding='utf-8') as file:
            print(f'{text_file} successfully opened.')
            return file.read()
    except Exception as e:
        print(f"Error opening '{text_file}': {e}")

def get_mp3_duration(file_path: str) -> float:
    """Get the duration of an MP3 file in seconds."""
    try:
        audio = MP3(file_path)
        return audio.info.length
    except Exception as e:
        print(f"Error getting duration for {file_path}: {e}")
        return 0.0

def split_text_into_sentences(text: str) -> list[str]:
    """Split text into sentences for processing."""
    sentences = re.split(r'(?<=[.!?])\s+', text)
    return [sentence.strip() for sentence in sentences if sentence.strip()]

async def generate_audio_and_measure_duration(text: str, output_file: str) -> float:
    """Generate audio file and return its duration in seconds."""
    try:
        print(f"Generating {output_file}...")
        communicate = edge_tts.Communicate(text, VOICE)
        await communicate.save(output_file)

        # Measure the actual duration
        duration = get_mp3_duration(output_file)
        duration_minutes = duration / 60
        print(f"{output_file} generated successfully. Duration: {duration_minutes:.2f} minutes")
        return duration
    except Exception as e:
        print(f"Error generating {output_file}: {e}")
        return 0.0

def calculate_optimal_chunk_size(current_words: int, actual_duration: float, target_duration: float) -> int:
    """Calculate optimal chunk size based on actual vs target duration."""
    if actual_duration <= 0:
        return current_words

    # Calculate the ratio and adjust chunk size
    duration_ratio = target_duration / actual_duration
    optimal_words = int(current_words * duration_ratio * 0.95)  # 5% safety margin

    # Ensure we don't make chunks too small or too large
    min_words = 1000
    max_words = 50000
    return max(min_words, min(max_words, optimal_words))

async def process_text_with_duration_control(text: str) -> None:
    """Process text with duration-based chunking."""
    sentences = split_text_into_sentences(text)
    if not sentences:
        print("No text to process.")
        return

    print(f"Processing {len(sentences)} sentences...")
    file_count = 1
    current_chunk_words = INITIAL_CHUNK_WORDS
    sentence_index = 0
    max_retries = 3

    while sentence_index < len(sentences):
        # Build chunk with current target word count
        current_chunk = []
        current_word_count = 0
        chunk_start_index = sentence_index

        # Add sentences until we reach the target word count
        while sentence_index < len(sentences) and current_word_count < current_chunk_words:
            sentence = sentences[sentence_index]
            sentence_words = len(sentence.split())

            # If adding this sentence would significantly exceed target, stop here
            if current_word_count > 0 and current_word_count + sentence_words > current_chunk_words * 1.2:
                break

            current_chunk.append(sentence)
            current_word_count += sentence_words
            sentence_index += 1

        if not current_chunk:
            # If we can't fit even one sentence, take it anyway
            current_chunk = [sentences[sentence_index]]
            current_word_count = len(sentences[sentence_index].split())
            sentence_index += 1

        chunk_text = " ".join(current_chunk)
        output_file = f"book_{file_count}.mp3"

        # Generate audio and measure duration with retry logic
        retry_count = 0
        actual_duration = 0.0

        while retry_count < max_retries:
            actual_duration = await generate_audio_and_measure_duration(chunk_text, output_file)

            if actual_duration > 0:
                break  # Success
            else:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"Retrying {output_file} (attempt {retry_count + 1}/{max_retries})...")
                    await asyncio.sleep(PAUSE_BETWEEN_REQUESTS)
                else:
                    print(f"Failed to generate {output_file} after {max_retries} attempts. Skipping...")
                    sentence_index += 1
                    continue

        if actual_duration > MAX_DURATION_SECONDS:
            print(f"Warning: {output_file} duration ({actual_duration/60:.2f} min) exceeds {MAX_DURATION_MINUTES} min limit!")

            # If this chunk is too long and we used more than one sentence, try with fewer sentences
            if len(current_chunk) > 1:
                print(f"Attempting to split {output_file} into smaller chunks...")
                # Remove the file and try again with half the sentences
                try:
                    os.remove(output_file)
                except OSError as e:
                    print(f"Warning: Could not remove {output_file}: {e}")
                sentence_index = chunk_start_index + len(current_chunk) // 2
                current_chunk_words = current_chunk_words // 2
                continue
            else:
                print(f"Single sentence exceeds duration limit. Keeping {output_file} as is.")

        # Update chunk size estimate for next iteration based on actual duration
        if actual_duration > 0:
            current_chunk_words = calculate_optimal_chunk_size(
                current_word_count, actual_duration, MAX_DURATION_SECONDS
            )
            print(f"Adjusted target chunk size to {current_chunk_words} words for next file")

        file_count += 1
        await asyncio.sleep(PAUSE_BETWEEN_REQUESTS)  # Brief pause between generations

async def amain() -> None:
    """Main function with input validation and error handling."""
    try:
        # Validate source file exists
        if not os.path.exists(SOURCE_FILE):
            print(f"Error: Source file '{SOURCE_FILE}' not found.")
            return

        text = read_text_from_file(SOURCE_FILE)
        if not text or not text.strip():
            print("No text found to process.")
            return

        # Basic text validation
        word_count = len(text.split())
        if word_count < 10:
            print(f"Warning: Text is very short ({word_count} words). Processing anyway...")
        elif word_count > 500000:
            print(f"Warning: Text is very long ({word_count} words). This may take a while...")

        print(f"Processing text with duration-based chunking (max {MAX_DURATION_MINUTES} minutes per file)...")
        print(f"Total words to process: {word_count}")

        await process_text_with_duration_control(text)
        print("Completed successfully!")

    except KeyboardInterrupt:
        print("\nProcess interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise

if __name__ == "__main__":
    loop = asyncio.get_event_loop_policy().get_event_loop()
    try:
        loop.run_until_complete(amain())
    finally:
        loop.close()